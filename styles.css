/* ===== CSS VARIABLES ===== */
:root {
    /* Light Theme Colors */
    --primary-color: #d4a574;
    --secondary-color: #f4e4d6;
    --accent-color: #c9a96e;
    --text-primary: #2c2c2c;
    --text-secondary: #666666;
    --background: #fefefe;
    --card-background: #ffffff;
    --border-color: #e8e8e8;
    --shadow: rgba(0, 0, 0, 0.1);
    --overlay: rgba(0, 0, 0, 0.4);
    
    /* Fonts */
    --font-primary: 'Playfair Display', serif;
    --font-secondary: 'Poppins', sans-serif;
    --font-script: 'Dancing Script', cursive;
    
    /* Transitions */
    --transition: all 0.3s ease;
    --transition-slow: all 0.6s ease;
}

/* Dark Theme */
[data-theme="dark"] {
    --primary-color: #d4a574;
    --secondary-color: #2a2a2a;
    --accent-color: #c9a96e;
    --text-primary: #f0f0f0;
    --text-secondary: #cccccc;
    --background: #1a1a1a;
    --card-background: #2a2a2a;
    --border-color: #404040;
    --shadow: rgba(0, 0, 0, 0.3);
    --overlay: rgba(0, 0, 0, 0.6);
}

/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-secondary);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background);
    transition: var(--transition);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: var(--transition);
    border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .navbar {
    background: rgba(26, 26, 26, 0.95);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo h2 {
    font-family: var(--font-script);
    font-size: 1.8rem;
    color: var(--primary-color);
    font-weight: 700;
}

.nav-menu {
    display: flex;
    list-style: none;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 0;
    background-color: var(--primary-color);
    transition: var(--transition);
}

.nav-link:hover::after {
    width: 100%;
}

.theme-toggle {
    background: none;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 8px 12px;
    border-radius: 50px;
    cursor: pointer;
    transition: var(--transition);
}

.theme-toggle:hover {
    background: var(--primary-color);
    color: white;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: var(--text-primary);
    margin: 3px 0;
    transition: var(--transition);
}

/* ===== HERO SECTION ===== */
.hero {
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-slideshow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.slide.active {
    opacity: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--overlay);
}

.hero-content {
    text-align: center;
    color: white;
    z-index: 2;
    position: relative;
    max-width: 800px;
    padding: 0 20px;
}

.hero-title {
    font-family: var(--font-primary);
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    font-weight: 300;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    animation: fadeInUp 1s ease-out 0.3s both;
}

.cta-button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 15px 40px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 1px;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.cta-button:hover {
    background: var(--accent-color);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.hero-nav {
    position: absolute;
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 30px;
    z-index: 3;
}

.hero-prev, .hero-next {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 2rem;
    padding: 15px 20px;
    cursor: pointer;
    border-radius: 50%;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.hero-prev:hover, .hero-next:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* ===== SECTION STYLES ===== */
section {
    padding: 100px 0;
}

.section-title {
    font-family: var(--font-primary);
    font-size: 3rem;
    text-align: center;
    margin-bottom: 3rem;
    color: var(--text-primary);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: var(--primary-color);
}

/* ===== ABOUT SECTION ===== */
.about {
    background: var(--secondary-color);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    align-items: center;
}

.about-image {
    text-align: center;
}

.portrait {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    object-fit: cover;
    border: 5px solid var(--primary-color);
    box-shadow: 0 20px 40px var(--shadow);
}

.signature {
    font-family: var(--font-script);
    font-size: 2.5rem;
    color: var(--primary-color);
    margin: 1rem 0;
    font-weight: 700;
}

.about-text p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
    line-height: 1.8;
}

.about-stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat {
    text-align: center;
}

.stat h3 {
    font-family: var(--font-primary);
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat p {
    color: var(--text-secondary);
    font-weight: 500;
}

/* ===== GALLERY SECTION ===== */
.gallery-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 10px 25px;
    border-radius: 25px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: white;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    cursor: pointer;
    transition: var(--transition);
    aspect-ratio: 1;
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px var(--shadow);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(212, 165, 116, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay i {
    color: white;
    font-size: 2rem;
}

/* ===== MODAL ===== */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    margin: auto;
    display: block;
    max-width: 90%;
    max-height: 90%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 10px;
}

.modal-close {
    position: absolute;
    top: 20px;
    right: 35px;
    color: white;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    z-index: 2001;
}

.modal-close:hover {
    color: var(--primary-color);
}

.modal-nav {
    position: absolute;
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 30px;
    z-index: 2001;
}

.modal-prev, .modal-next {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 2rem;
    padding: 15px 20px;
    cursor: pointer;
    border-radius: 50%;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.modal-prev:hover, .modal-next:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* ===== TESTIMONIALS SECTION ===== */
.testimonials {
    background: var(--secondary-color);
}

.testimonials-slider {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.testimonial-slide {
    display: none;
    animation: fadeIn 0.5s ease;
}

.testimonial-slide.active {
    display: block;
}

.testimonial-content p {
    font-size: 1.3rem;
    font-style: italic;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.testimonial-author h4 {
    font-family: var(--font-primary);
    font-size: 1.2rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.testimonial-author span {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.testimonial-dots {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 2rem;
}

.dot {
    height: 12px;
    width: 12px;
    background-color: var(--border-color);
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
}

.dot.active,
.dot:hover {
    background-color: var(--primary-color);
}

/* ===== PRICING SECTION ===== */
.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.pricing-card {
    background: var(--card-background);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px var(--shadow);
    transition: var(--transition);
    position: relative;
    border: 2px solid transparent;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px var(--shadow);
}

.pricing-card.featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.card-header h3 {
    font-family: var(--font-primary);
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.price {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.popular {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: white;
    padding: 5px 20px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.features {
    list-style: none;
    margin: 2rem 0;
}

.features li {
    padding: 0.5rem 0;
    color: var(--text-secondary);
}

.features i {
    color: var(--primary-color);
    margin-right: 10px;
}

.package-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    width: 100%;
}

.package-btn:hover {
    background: var(--accent-color);
    transform: translateY(-2px);
}

/* ===== CONTACT SECTION ===== */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info h3 {
    font-family: var(--font-primary);
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.contact-info p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.contact-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
    width: 20px;
}

.contact-form {
    background: var(--card-background);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px var(--shadow);
}

.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    font-size: 1rem;
    transition: var(--transition);
    background: var(--background);
    color: var(--text-primary);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.error-message {
    color: #e74c3c;
    font-size: 0.8rem;
    margin-top: 5px;
    display: block;
}

.submit-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 25px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    width: 100%;
    font-size: 1.1rem;
}

.submit-btn:hover {
    background: var(--accent-color);
    transform: translateY(-2px);
}

/* ===== FOOTER ===== */
.footer {
    background: var(--text-primary);
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.footer-logo h3 {
    font-family: var(--font-script);
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.footer-logo p {
    color: #cccccc;
    font-size: 0.9rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition);
}

.social-link:hover {
    background: var(--accent-color);
    transform: translateY(-2px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #444;
    color: #cccccc;
    font-size: 0.9rem;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: var(--transition-slow);
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--background);
        width: 100%;
        text-align: center;
        transition: var(--transition);
        box-shadow: 0 10px 27px var(--shadow);
        padding: 2rem 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .about-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .about-stats {
        justify-content: center;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }
    
    .hero-nav {
        padding: 0 20px;
    }
    
    .hero-prev, .hero-next {
        font-size: 1.5rem;
        padding: 10px 15px;
    }
    
    .pricing-card.featured {
        transform: none;
    }
    
    .pricing-card.featured:hover {
        transform: translateY(-10px);
    }
}
